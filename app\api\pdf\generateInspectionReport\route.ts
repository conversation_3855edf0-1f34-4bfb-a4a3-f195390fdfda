import { NextRequest, NextResponse } from 'next/server';
import { PropertyModel } from '@/app/models/Property';
import { PropertyBusiness } from '@/app/api/property/business';
import { generatePdfFromHtml } from '@/app/utils/generatePdfFromHtml';
import { renderInspectionTemplate } from './renderTemplate';

async function generateInspectionReport(property: PropertyModel): Promise<Buffer> {
    try {
        // Render React component to HTML using helper function
        const html = renderInspectionTemplate(property);

        // Generate PDF from HTML
        const pdfBuffer = await generatePdfFromHtml(html);

        return pdfBuffer;
    } catch (error) {
        console.error('Error generating inspection report:', error);
        throw error;
    }
}

export async function POST(request: NextRequest) {
    try {
        const { propertyId } = await request.json();

        if (!propertyId) {
            return NextResponse.json({ error: 'Property ID is required' }, { status: 400 });
        }

        // Get property data from database
        const propertyData = await PropertyBusiness.Get(propertyId);

        if (!propertyData) {
            return NextResponse.json({ error: 'Property not found' }, { status: 404 });
        }

        // Convert to PropertyModel instance
        const property = new PropertyModel(propertyData);

        // Generate PDF from React component
        const pdfBuffer = await generateInspectionReport(property);

        // Return PDF as base64-encoded JSON (compatible with JC_Post)
        const base64Pdf = pdfBuffer.toString('base64');
        return NextResponse.json({
            result: {
                pdfData: base64Pdf,
                filename: `property-report-${property.Id}.pdf`,
                contentType: 'application/pdf'
            }
        }, { status: 200 });
    } catch (error) {
        console.error('Error generating PDF:', error);
        return NextResponse.json({ error: 'Failed to generate PDF' }, { status: 500 });
    }
}


