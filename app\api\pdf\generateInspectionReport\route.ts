import { NextRequest, NextResponse } from 'next/server';
import { PropertyModel } from '@/app/models/Property';
import { PropertyBusiness } from '@/app/api/property/business';
import { renderComponentToHtml } from '@/app/utils/renderComponentToHtml';
import { generatePdfFromHtml } from '@/app/utils/generatePdfFromHtml';
import Template_InspectionPdf from '@/templates/Template_InspectionPdf';

async function generateInspectionReport(property: PropertyModel): Promise<Buffer> {
    try {
        // Use the React component template to generate HTML
        const html = renderComponentToHtml(Template_InspectionPdf, property);

        // Generate PDF from HTML
        const pdfBuffer = await generatePdfFromHtml(html);

        return pdfBuffer;
    } catch (error) {
        console.error('Error generating inspection report:', error);
        throw error;
    }
}

export async function POST(request: NextRequest) {
    try {
        const { propertyId } = await request.json();

        if (!propertyId) {
            return NextResponse.json({ error: 'Property ID is required' }, { status: 400 });
        }

        // Get property data from database
        const propertyData = await PropertyBusiness.Get(propertyId);

        if (!propertyData) {
            return NextResponse.json({ error: 'Property not found' }, { status: 404 });
        }

        // Convert to PropertyModel instance
        const property = new PropertyModel(propertyData);

        // Generate PDF from React component
        const pdfBuffer = await generateInspectionReport(property);

        // Return PDF as response
        return new NextResponse(pdfBuffer, {
            status: 200,
            headers: {
                'Content-Type': 'application/pdf',
                'Content-Disposition': `attachment; filename="property-report-${property.Id}.pdf"`,
            },
        });
    } catch (error) {
        console.error('Error generating PDF:', error);
        return NextResponse.json({ error: 'Failed to generate PDF' }, { status: 500 });
    }
}


