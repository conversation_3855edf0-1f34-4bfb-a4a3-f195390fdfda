import { renderToStaticMarkup } from "react-dom/server";
import React from "react";

// ---------------------- //
// - SERVER-SIDE UTILS  - //
// ---------------------- //

export class JC_Utils_Server {

    /**
     * Render a React component to HTML string (server-side only)
     * @param Component The React component to render
     * @param props The props to pass to the component
     * @returns HTML string with DOCTYPE
     */
    static renderComponentToHtml<P>(
        Component: React.ComponentType<P>,
        props: P
    ): string {
        // Cast Component to any here to fix TS error
        const element = React.createElement(Component as React.ComponentType<any>, props);
        return "<!DOCTYPE html>" + renderToStaticMarkup(element);
    }
}