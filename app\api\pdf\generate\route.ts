import { NextRequest, NextResponse } from 'next/server';
import { PropertyModel } from '@/app/models/Property';
import { PropertyBusiness } from '@/app/api/property/business';
import puppeteer from 'puppeteer';

function generateInspectionReportHtml(property: PropertyModel): string {
    return `<!DOCTYPE html>
<html>
<head>
    <title>Property Inspection Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            color: #2c3e50;
            font-size: 28px;
        }
        .header h2 {
            margin: 10px 0 0 0;
            color: #7f8c8d;
            font-weight: normal;
            font-size: 18px;
        }
        .section-title {
            background-color: #3498db;
            color: white;
            padding: 10px 15px;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: bold;
        }
        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .detail-item {
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .detail-label {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .detail-value {
            color: #555;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            border-top: 1px solid #ddd;
            padding-top: 20px;
            color: #7f8c8d;
            font-size: 12px;
        }
        .generated-date {
            margin-top: 30px;
            text-align: right;
            color: #7f8c8d;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Property Inspection Report</h1>
        <h2>${property.Address || 'Property Address'}</h2>
    </div>

    <div class="section-title">Property Information</div>

    <div class="details-grid">
        <div class="detail-item">
            <div class="detail-label">Address</div>
            <div class="detail-value">${property.Address || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Building Type</div>
            <div class="detail-value">${property.BuildingTypeCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Company/Strata Title</div>
            <div class="detail-value">${property.CompanyStrataTitleCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Number of Bedrooms</div>
            <div class="detail-value">${property.NumBedroomsCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Orientation</div>
            <div class="detail-value">${property.OrientationCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Storeys</div>
            <div class="detail-value">${property.StoreysCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Furnished</div>
            <div class="detail-value">${property.FurnishedCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Occupied</div>
            <div class="detail-value">${property.OccupiedCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Floor</div>
            <div class="detail-value">${property.FloorCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Roof</div>
            <div class="detail-value">${property.RoofCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Walls</div>
            <div class="detail-value">${property.WallsCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Weather</div>
            <div class="detail-value">${property.WeatherCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Other Building Elements</div>
            <div class="detail-value">${property.OtherBuildingElementsCode || 'Not specified'}</div>
        </div>

        <div class="detail-item">
            <div class="detail-label">Other Timber Building Elements</div>
            <div class="detail-value">${property.OtherTimberBldgElementsCode || 'Not specified'}</div>
        </div>
    </div>

    ${property.RoomsListJson ? `
        <div class="section-title">Rooms</div>
        <div class="detail-item">
            <div class="detail-label">Room List</div>
            <div class="detail-value">
                ${(() => {
                    try {
                        const rooms = JSON.parse(property.RoomsListJson);
                        return Array.isArray(rooms) ? rooms.join(', ') : 'Invalid room data';
                    } catch {
                        return 'Invalid room data';
                    }
                })()}
            </div>
        </div>
    ` : ''}

    <div class="generated-date">
        Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
    </div>

    <div class="footer">
        <p>This report was generated automatically by the Property Inspection System</p>
        <p>Property ID: ${property.Id}</p>
    </div>
</body>
</html>`;
}

async function generateInspectionReport(property: PropertyModel): Promise<Buffer> {
    let browser;
    try {
        // Generate HTML from property data
        const html = generateInspectionReportHtml(property);

        // Launch browser
        browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const page = await browser.newPage();

        // Set content
        await page.setContent(html, {
            waitUntil: 'networkidle0'
        });

        // Generate PDF
        const pdfBuffer = await page.pdf({
            format: 'A4',
            margin: { top: '1cm', right: '1cm', bottom: '1cm', left: '1cm' },
            printBackground: true,
            landscape: false
        });

        return Buffer.from(pdfBuffer);
    } catch (error) {
        console.error('Error generating inspection report:', error);
        throw error;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

export async function POST(request: NextRequest) {
    try {
        const { propertyId } = await request.json();

        if (!propertyId) {
            return NextResponse.json({ error: 'Property ID is required' }, { status: 400 });
        }

        // Get property data from database
        const propertyData = await PropertyBusiness.Get(propertyId);

        if (!propertyData) {
            return NextResponse.json({ error: 'Property not found' }, { status: 404 });
        }

        // Convert to PropertyModel instance
        const property = new PropertyModel(propertyData);

        // Generate PDF from React component
        const pdfBuffer = await generateInspectionReport(property);

        // Return PDF as response
        return new NextResponse(pdfBuffer, {
            status: 200,
            headers: {
                'Content-Type': 'application/pdf',
                'Content-Disposition': `attachment; filename="property-report-${property.Id}.pdf"`,
            },
        });
    } catch (error) {
        console.error('Error generating PDF:', error);
        return NextResponse.json({ error: 'Failed to generate PDF' }, { status: 500 });
    }
}


